import React, { useState } from 'react'
import { Form, Input, Select, Button, Space, Card } from 'antd'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'
import type { DescriptionSearchParams } from '../../../services/api'

const { Option } = Select

export interface SearchFiltersProps {
  onSearch: (params: Omit<DescriptionSearchParams, 'pageNum' | 'pageSize'>) => void
  onReset: () => void
  loading?: boolean
}

export interface SearchFormValues {
  namespace?: string
  publishStatus?: string
  materialVersion?: string
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  onSearch,
  onReset,
  loading = false,
}) => {
  const [form] = Form.useForm<SearchFormValues>()
  const [expanded, setExpanded] = useState(false)

  // 处理搜索
  const handleSearch = () => {
    const values = form.getFieldsValue()
    onSearch(values)
  }

  // 处理重置
  const handleReset = () => {
    form.resetFields()
    onReset()
  }

  // 处理回车搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <Card size="small" style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleSearch}
        style={{ width: '100%' }}
      >
        <Form.Item
          name="namespace"
          style={{ marginBottom: 8 }}
        >
          <Input
            placeholder="搜索命名空间"
            allowClear
            onKeyPress={handleKeyPress}
            style={{ width: 200 }}
          />
        </Form.Item>

        <Form.Item
          name="materialVersion"
          style={{ marginBottom: 8 }}
        >
          <Input
            placeholder="搜索版本号"
            allowClear
            onKeyPress={handleKeyPress}
            style={{ width: 150 }}
          />
        </Form.Item>

        <Form.Item
          name="publishStatus"
          style={{ marginBottom: 8 }}
        >
          <Select
            placeholder="发布状态"
            allowClear
            style={{ width: 120 }}
          >
            <Option value="1">已发布</Option>
            <Option value="0">未发布</Option>
          </Select>
        </Form.Item>

        <Form.Item style={{ marginBottom: 8 }}>
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
            >
              搜索
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  )
}

export default SearchFilters
