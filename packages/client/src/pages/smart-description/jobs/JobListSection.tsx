import { useState, useCallback, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Button, Typography, Pagination, Alert, Row, Col, Card, Statistic, Table, Space } from 'antd'
import { ReloadOutlined, EyeOutlined, FileTextOutlined, HistoryOutlined } from '@ant-design/icons'
import { message } from 'antd'
import { materialSmartDescriptionJobApi } from '../../../services/api'
import { formatTime, getStagingTag } from './shared/utils'
import { DEFAULT_PAGE_SIZE } from './shared/constants'
import SearchFilters from './SearchFilters'
import type {
  MaterialSmartDescriptionJob,
  PaginationParams,
  JobSearchParams,
  JobStats,
  JobListSectionProps,
  JobListSectionRef,
} from './shared/types'
import styles from './MaterialSmartDescriptionJobList.module.less'

const { Title, Text } = Typography

const JobListSection = forwardRef<JobListSectionRef, JobListSectionProps>(({
  onViewDetail,
  onViewHistory,
}, ref) => {
  // 列表数据状态
  const [data, setData] = useState<MaterialSmartDescriptionJob[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  // 搜索筛选状态
  const [searchParams, setSearchParams] = useState<Omit<JobSearchParams, 'pageNum' | 'pageSize'>>({})

  // 统计数据状态
  const [statsData, setStatsData] = useState<JobStats>({
    pending: 0,
    succeeded: 0,
    failed: 0,
    total: 0,
  })

  // 获取列表数据
  const fetchData = useCallback(async (params?: Partial<JobSearchParams>) => {
    setLoading(true)
    try {
      const response = await materialSmartDescriptionJobApi.getJobs({
        pageNum: currentPage,
        pageSize,
        ...searchParams,
        ...params,
      })
      if (response.data.code === 1) {
        setData(response.data.data.list)
        setTotal(response.data.data.total)
      }
      else {
        message.error(response.data.message || '获取数据失败')
      }
    }
    catch (error) {
      console.error('获取数据失败:', error)
      message.error('获取数据失败，请稍后重试')
    }
    finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchParams])

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await materialSmartDescriptionJobApi.getJobStats()
      if (response.data.code === 1) {
        setStatsData(response.data.data)
      }
    }
    catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }, [])

  // 分页变化处理
  const handlePageChange = useCallback((page: number, size?: number) => {
    if (size && size !== pageSize) {
      setPageSize(size)
      setCurrentPage(1)
    }
    else {
      setCurrentPage(page)
    }
  }, [pageSize])

  // 搜索处理
  const handleSearch = useCallback((params: Omit<JobSearchParams, 'pageNum' | 'pageSize'>) => {
    setSearchParams(params)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 重置搜索
  const handleResetSearch = useCallback(() => {
    setSearchParams({})
    setCurrentPage(1)
  }, [])

  // 刷新数据
  const handleRefresh = useCallback(() => {
    fetchData({ pageNum: currentPage, pageSize })
    fetchStats()
  }, [fetchData, fetchStats, currentPage, pageSize])

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    refresh: () => fetchData({ pageNum: currentPage, pageSize }),
    refreshStats: fetchStats,
  }), [fetchData, fetchStats, currentPage, pageSize])

  // 初始化数据
  useEffect(() => {
    fetchData({ pageNum: currentPage, pageSize })
    fetchStats()
  }, [currentPage, pageSize, searchParams, fetchData, fetchStats])

  // 表格列定义
  const columns = [
    {
      title: '物料信息',
      key: 'material',
      width: 250,
      render: (record: MaterialSmartDescriptionJob) => (
        <div>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ fontSize: '14px', color: '#262626' }}>
              {record.materialDetail.title}
            </Text>
          </div>
          <div style={{ marginBottom: 6 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>组件名称：</Text>
            <Text code style={{ fontSize: '12px', background: '#e6f7ff', color: '#52c41a', padding: '2px 6px', borderRadius: '4px', fontWeight: 500 }}>
              {record.materialDetail.name}
            </Text>
          </div>
          <div style={{ marginBottom: 6 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>命名空间：</Text>
            <Text code style={{ fontSize: '12px', background: '#f6f8fa', padding: '2px 6px', borderRadius: '4px' }}>
              {record.materialDetail.namespace}
            </Text>
          </div>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>版本：</Text>
            <Text code style={{ fontSize: '12px', background: '#f6f8fa', padding: '2px 6px', borderRadius: '4px' }}>
              {record.materialVersion}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '任务状态',
      key: 'status',
      width: 200,
      render: (record: MaterialSmartDescriptionJob) => {
        return (
          <div>
            <div style={{ marginBottom: 8 }}>
              {getStagingTag(record.staging)}
            </div>
            {record.failedReason && (
              <Text
                type="danger"
                style={{
                  fontSize: '12px',
                  lineHeight: '1.4',
                  display: 'block',
                  background: '#fff2f0',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  border: '1px solid #ffccc7',
                }}
              >
                {record.failedReason}
              </Text>
            )}
          </div>
        )
      },
    },
    {
      title: '执行信息',
      key: 'execution',
      width: 200,
      render: (record: MaterialSmartDescriptionJob) => (
        <div>
          <div style={{ marginBottom: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>任务ID：</Text>
            <Text
              code
              style={{
                fontSize: '12px',
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontWeight: 500,
              }}
            >
              {record.id}
            </Text>
          </div>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>创建时间：</Text>
            <Text style={{ fontSize: '12px', color: '#595959' }}>
              {formatTime(record.createTime)}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '结果',
      key: 'result',
      width: 150,
      render: (record: MaterialSmartDescriptionJob) => (
        <div>
          {record.resultId && (
            <div style={{ marginBottom: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>结果ID：</Text>
              <Text
                code
                style={{
                  fontSize: '12px',
                  background: '#f6f8fa',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontWeight: 500,
                }}
              >
                {record.resultId}
              </Text>
            </div>
          )}
          <Space direction="vertical" size={4}>
            {record.rawResult && (
              <Button
                type="link"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => window.open(record.rawResult!, '_blank')}
                style={{
                  padding: '2px 8px',
                  height: 'auto',
                  fontSize: '12px',
                  color: '#1890ff',
                }}
              >
                查看结果
              </Button>
            )}
            {record.rawConversation && (
              <Button
                type="link"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => window.open(record.rawConversation!, '_blank')}
                style={{
                  padding: '2px 8px',
                  height: 'auto',
                  fontSize: '12px',
                  color: '#1890ff',
                }}
              >
                查看对话
              </Button>
            )}
          </Space>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: MaterialSmartDescriptionJob) => (
        <Space size={4} direction="vertical">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onViewDetail(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#1890ff',
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => onViewHistory(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#722ed1',
            }}
          >
            历史记录
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Title level={4} className={styles.title}>
          物料智能描述任务
        </Title>
        <div className={styles.actions}>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className={styles.statusCard}>
        <Row gutter={[16, 16]}>
          <Col xs={12} sm={12} md={6} lg={6}>
            <Card hoverable>
              <Statistic
                title="总任务数"
                value={statsData.total}
                valueStyle={{ color: '#262626', fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={12} md={6} lg={6}>
            <Card hoverable>
              <Statistic
                title="等待中"
                value={statsData.pending}
                valueStyle={{ color: '#faad14', fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={12} md={6} lg={6}>
            <Card hoverable>
              <Statistic
                title="成功"
                value={statsData.succeeded}
                valueStyle={{ color: '#52c41a', fontWeight: 600 }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={12} md={6} lg={6}>
            <Card hoverable>
              <Statistic
                title="失败"
                value={statsData.failed}
                valueStyle={{ color: '#ff4d4f', fontWeight: 600 }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* 搜索筛选区域 */}
      <SearchFilters
        onSearch={handleSearch}
        onReset={handleResetSearch}
        loading={loading}
      />

      {/* 说明信息 */}
      <div className={styles.filterSection}>
        <Alert
          message="数据说明"
          description="列表中显示的是每个物料版本的最新任务记录。任务状态包括：执行中、成功、失败。点击「历史记录」可查看同一物料版本的所有执行记录。支持按命名空间、版本号、任务状态进行搜索筛选。"
          type="info"
          showIcon
          closable={false}
        />
      </div>

      {/* 表格和分页 */}
      <div className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>
    </div>
  )
})

JobListSection.displayName = 'JobListSection'

export default JobListSection
